{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "jsx": "preserve",
    "noEmit": true,
    "emitDeclarationOnly": false,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "types": [
      "jest",
      "node"
    ],
    "baseUrl": ".."
  },
  "include": [
    // Next.js app files
    "../libs/data-access/src/**/*.ts",
    "../libs/domain/src/**/*.ts",
    "../libs/ui/src/**/*.ts",
    "../libs/ui/src/**/*.tsx",
    "../libs/util/src/**/*.ts",
    ".next/types/**/*.ts",
    // Shared libraries (needed for type checking)
    "app/**/*.js",
    "app/**/*.jsx",
    "app/**/*.ts",
    "app/**/*.tsx",
    "next-env.d.ts",
    "dist/types/**/*.ts"
  ],
  "exclude": [
    "out-tsc",
    "dist",
    "node_modules",
    "jest.config.ts",
    "app/**/*.spec.ts",
    "app/**/*.test.ts",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    ".next",
    "eslint.config.js",
    "eslint.config.cjs",
    "eslint.config.mjs"
  ],
  "references": [
    {
      "path": "../libs/domain"
    },
    {
      "path": "../libs/data-access"
    },
    {
      "path": "../libs/util"
    },
    {
      "path": "../libs/ui"
    }
  ]
}

drop trigger if exists "audit_app_packages" on "public"."app_packages";

drop trigger if exists "audit_billing_entities" on "public"."billing_entities";

drop trigger if exists "audit_color_themes" on "public"."color_themes";

drop trigger if exists "audit_documents" on "public"."documents";

drop trigger if exists "audit_event_participants" on "public"."event_participants";

drop trigger if exists "audit_events" on "public"."events";

drop trigger if exists "audit_group_members" on "public"."group_members";

drop trigger if exists "audit_groups" on "public"."groups";

drop trigger if exists "audit_invoices" on "public"."invoices";

drop trigger if exists "audit_notifications" on "public"."notifications";

drop trigger if exists "audit_organization_access" on "public"."organization_access";

drop trigger if exists "audit_organizations" on "public"."organizations";

drop trigger if exists "audit_package_feature_assignment" on "public"."package_feature_assignment";

drop trigger if exists "audit_package_features" on "public"."package_features";

drop trigger if exists "audit_poll_options" on "public"."poll_options";

drop trigger if exists "audit_poll_votes" on "public"."poll_votes";

drop trigger if exists "audit_polls" on "public"."polls";

drop trigger if exists "audit_profile_billing_assignment" on "public"."profile_billing_assignment";

drop trigger if exists "audit_profiles" on "public"."profiles";

drop trigger if exists "audit_user_role_assignment" on "public"."user_role_assignment";

drop trigger if exists "audit_user_roles" on "public"."user_roles";

drop policy "Superadmin full access to audit_log" on "public"."audit_log";

drop policy "Superadmin bypass access on organizations" on "public"."organizations";

revoke delete on table "public"."app_packages" from "anon";

revoke insert on table "public"."app_packages" from "anon";

revoke references on table "public"."app_packages" from "anon";

revoke select on table "public"."app_packages" from "anon";

revoke trigger on table "public"."app_packages" from "anon";

revoke truncate on table "public"."app_packages" from "anon";

revoke update on table "public"."app_packages" from "anon";

revoke delete on table "public"."app_packages" from "authenticated";

revoke insert on table "public"."app_packages" from "authenticated";

revoke references on table "public"."app_packages" from "authenticated";

revoke select on table "public"."app_packages" from "authenticated";

revoke trigger on table "public"."app_packages" from "authenticated";

revoke truncate on table "public"."app_packages" from "authenticated";

revoke update on table "public"."app_packages" from "authenticated";

revoke delete on table "public"."app_packages" from "service_role";

revoke insert on table "public"."app_packages" from "service_role";

revoke references on table "public"."app_packages" from "service_role";

revoke select on table "public"."app_packages" from "service_role";

revoke trigger on table "public"."app_packages" from "service_role";

revoke truncate on table "public"."app_packages" from "service_role";

revoke update on table "public"."app_packages" from "service_role";

revoke delete on table "public"."audit_log" from "anon";

revoke insert on table "public"."audit_log" from "anon";

revoke references on table "public"."audit_log" from "anon";

revoke select on table "public"."audit_log" from "anon";

revoke trigger on table "public"."audit_log" from "anon";

revoke truncate on table "public"."audit_log" from "anon";

revoke update on table "public"."audit_log" from "anon";

revoke delete on table "public"."audit_log" from "authenticated";

revoke insert on table "public"."audit_log" from "authenticated";

revoke references on table "public"."audit_log" from "authenticated";

revoke select on table "public"."audit_log" from "authenticated";

revoke trigger on table "public"."audit_log" from "authenticated";

revoke truncate on table "public"."audit_log" from "authenticated";

revoke update on table "public"."audit_log" from "authenticated";

revoke delete on table "public"."audit_log" from "service_role";

revoke insert on table "public"."audit_log" from "service_role";

revoke references on table "public"."audit_log" from "service_role";

revoke select on table "public"."audit_log" from "service_role";

revoke trigger on table "public"."audit_log" from "service_role";

revoke truncate on table "public"."audit_log" from "service_role";

revoke update on table "public"."audit_log" from "service_role";

revoke delete on table "public"."billing_entities" from "anon";

revoke insert on table "public"."billing_entities" from "anon";

revoke references on table "public"."billing_entities" from "anon";

revoke select on table "public"."billing_entities" from "anon";

revoke trigger on table "public"."billing_entities" from "anon";

revoke truncate on table "public"."billing_entities" from "anon";

revoke update on table "public"."billing_entities" from "anon";

revoke delete on table "public"."billing_entities" from "authenticated";

revoke insert on table "public"."billing_entities" from "authenticated";

revoke references on table "public"."billing_entities" from "authenticated";

revoke select on table "public"."billing_entities" from "authenticated";

revoke trigger on table "public"."billing_entities" from "authenticated";

revoke truncate on table "public"."billing_entities" from "authenticated";

revoke update on table "public"."billing_entities" from "authenticated";

revoke delete on table "public"."billing_entities" from "service_role";

revoke insert on table "public"."billing_entities" from "service_role";

revoke references on table "public"."billing_entities" from "service_role";

revoke select on table "public"."billing_entities" from "service_role";

revoke trigger on table "public"."billing_entities" from "service_role";

revoke truncate on table "public"."billing_entities" from "service_role";

revoke update on table "public"."billing_entities" from "service_role";

revoke delete on table "public"."color_themes" from "anon";

revoke insert on table "public"."color_themes" from "anon";

revoke references on table "public"."color_themes" from "anon";

revoke select on table "public"."color_themes" from "anon";

revoke trigger on table "public"."color_themes" from "anon";

revoke truncate on table "public"."color_themes" from "anon";

revoke update on table "public"."color_themes" from "anon";

revoke delete on table "public"."color_themes" from "authenticated";

revoke insert on table "public"."color_themes" from "authenticated";

revoke references on table "public"."color_themes" from "authenticated";

revoke select on table "public"."color_themes" from "authenticated";

revoke trigger on table "public"."color_themes" from "authenticated";

revoke truncate on table "public"."color_themes" from "authenticated";

revoke update on table "public"."color_themes" from "authenticated";

revoke delete on table "public"."color_themes" from "service_role";

revoke insert on table "public"."color_themes" from "service_role";

revoke references on table "public"."color_themes" from "service_role";

revoke select on table "public"."color_themes" from "service_role";

revoke trigger on table "public"."color_themes" from "service_role";

revoke truncate on table "public"."color_themes" from "service_role";

revoke update on table "public"."color_themes" from "service_role";

revoke delete on table "public"."documents" from "anon";

revoke insert on table "public"."documents" from "anon";

revoke references on table "public"."documents" from "anon";

revoke select on table "public"."documents" from "anon";

revoke trigger on table "public"."documents" from "anon";

revoke truncate on table "public"."documents" from "anon";

revoke update on table "public"."documents" from "anon";

revoke delete on table "public"."documents" from "authenticated";

revoke insert on table "public"."documents" from "authenticated";

revoke references on table "public"."documents" from "authenticated";

revoke select on table "public"."documents" from "authenticated";

revoke trigger on table "public"."documents" from "authenticated";

revoke truncate on table "public"."documents" from "authenticated";

revoke update on table "public"."documents" from "authenticated";

revoke delete on table "public"."documents" from "service_role";

revoke insert on table "public"."documents" from "service_role";

revoke references on table "public"."documents" from "service_role";

revoke select on table "public"."documents" from "service_role";

revoke trigger on table "public"."documents" from "service_role";

revoke truncate on table "public"."documents" from "service_role";

revoke update on table "public"."documents" from "service_role";

revoke delete on table "public"."event_participants" from "anon";

revoke insert on table "public"."event_participants" from "anon";

revoke references on table "public"."event_participants" from "anon";

revoke select on table "public"."event_participants" from "anon";

revoke trigger on table "public"."event_participants" from "anon";

revoke truncate on table "public"."event_participants" from "anon";

revoke update on table "public"."event_participants" from "anon";

revoke delete on table "public"."event_participants" from "authenticated";

revoke insert on table "public"."event_participants" from "authenticated";

revoke references on table "public"."event_participants" from "authenticated";

revoke select on table "public"."event_participants" from "authenticated";

revoke trigger on table "public"."event_participants" from "authenticated";

revoke truncate on table "public"."event_participants" from "authenticated";

revoke update on table "public"."event_participants" from "authenticated";

revoke delete on table "public"."event_participants" from "service_role";

revoke insert on table "public"."event_participants" from "service_role";

revoke references on table "public"."event_participants" from "service_role";

revoke select on table "public"."event_participants" from "service_role";

revoke trigger on table "public"."event_participants" from "service_role";

revoke truncate on table "public"."event_participants" from "service_role";

revoke update on table "public"."event_participants" from "service_role";

revoke delete on table "public"."events" from "anon";

revoke insert on table "public"."events" from "anon";

revoke references on table "public"."events" from "anon";

revoke select on table "public"."events" from "anon";

revoke trigger on table "public"."events" from "anon";

revoke truncate on table "public"."events" from "anon";

revoke update on table "public"."events" from "anon";

revoke delete on table "public"."events" from "authenticated";

revoke insert on table "public"."events" from "authenticated";

revoke references on table "public"."events" from "authenticated";

revoke select on table "public"."events" from "authenticated";

revoke trigger on table "public"."events" from "authenticated";

revoke truncate on table "public"."events" from "authenticated";

revoke update on table "public"."events" from "authenticated";

revoke delete on table "public"."events" from "service_role";

revoke insert on table "public"."events" from "service_role";

revoke references on table "public"."events" from "service_role";

revoke select on table "public"."events" from "service_role";

revoke trigger on table "public"."events" from "service_role";

revoke truncate on table "public"."events" from "service_role";

revoke update on table "public"."events" from "service_role";

revoke delete on table "public"."group_members" from "anon";

revoke insert on table "public"."group_members" from "anon";

revoke references on table "public"."group_members" from "anon";

revoke select on table "public"."group_members" from "anon";

revoke trigger on table "public"."group_members" from "anon";

revoke truncate on table "public"."group_members" from "anon";

revoke update on table "public"."group_members" from "anon";

revoke delete on table "public"."group_members" from "authenticated";

revoke insert on table "public"."group_members" from "authenticated";

revoke references on table "public"."group_members" from "authenticated";

revoke select on table "public"."group_members" from "authenticated";

revoke trigger on table "public"."group_members" from "authenticated";

revoke truncate on table "public"."group_members" from "authenticated";

revoke update on table "public"."group_members" from "authenticated";

revoke delete on table "public"."group_members" from "service_role";

revoke insert on table "public"."group_members" from "service_role";

revoke references on table "public"."group_members" from "service_role";

revoke select on table "public"."group_members" from "service_role";

revoke trigger on table "public"."group_members" from "service_role";

revoke truncate on table "public"."group_members" from "service_role";

revoke update on table "public"."group_members" from "service_role";

revoke delete on table "public"."groups" from "anon";

revoke insert on table "public"."groups" from "anon";

revoke references on table "public"."groups" from "anon";

revoke select on table "public"."groups" from "anon";

revoke trigger on table "public"."groups" from "anon";

revoke truncate on table "public"."groups" from "anon";

revoke update on table "public"."groups" from "anon";

revoke delete on table "public"."groups" from "authenticated";

revoke insert on table "public"."groups" from "authenticated";

revoke references on table "public"."groups" from "authenticated";

revoke select on table "public"."groups" from "authenticated";

revoke trigger on table "public"."groups" from "authenticated";

revoke truncate on table "public"."groups" from "authenticated";

revoke update on table "public"."groups" from "authenticated";

revoke delete on table "public"."groups" from "service_role";

revoke insert on table "public"."groups" from "service_role";

revoke references on table "public"."groups" from "service_role";

revoke select on table "public"."groups" from "service_role";

revoke trigger on table "public"."groups" from "service_role";

revoke truncate on table "public"."groups" from "service_role";

revoke update on table "public"."groups" from "service_role";

revoke delete on table "public"."invoices" from "anon";

revoke insert on table "public"."invoices" from "anon";

revoke references on table "public"."invoices" from "anon";

revoke select on table "public"."invoices" from "anon";

revoke trigger on table "public"."invoices" from "anon";

revoke truncate on table "public"."invoices" from "anon";

revoke update on table "public"."invoices" from "anon";

revoke delete on table "public"."invoices" from "authenticated";

revoke insert on table "public"."invoices" from "authenticated";

revoke references on table "public"."invoices" from "authenticated";

revoke select on table "public"."invoices" from "authenticated";

revoke trigger on table "public"."invoices" from "authenticated";

revoke truncate on table "public"."invoices" from "authenticated";

revoke update on table "public"."invoices" from "authenticated";

revoke delete on table "public"."invoices" from "service_role";

revoke insert on table "public"."invoices" from "service_role";

revoke references on table "public"."invoices" from "service_role";

revoke select on table "public"."invoices" from "service_role";

revoke trigger on table "public"."invoices" from "service_role";

revoke truncate on table "public"."invoices" from "service_role";

revoke update on table "public"."invoices" from "service_role";

revoke delete on table "public"."notifications" from "anon";

revoke insert on table "public"."notifications" from "anon";

revoke references on table "public"."notifications" from "anon";

revoke select on table "public"."notifications" from "anon";

revoke trigger on table "public"."notifications" from "anon";

revoke truncate on table "public"."notifications" from "anon";

revoke update on table "public"."notifications" from "anon";

revoke delete on table "public"."notifications" from "authenticated";

revoke insert on table "public"."notifications" from "authenticated";

revoke references on table "public"."notifications" from "authenticated";

revoke select on table "public"."notifications" from "authenticated";

revoke trigger on table "public"."notifications" from "authenticated";

revoke truncate on table "public"."notifications" from "authenticated";

revoke update on table "public"."notifications" from "authenticated";

revoke delete on table "public"."notifications" from "service_role";

revoke insert on table "public"."notifications" from "service_role";

revoke references on table "public"."notifications" from "service_role";

revoke select on table "public"."notifications" from "service_role";

revoke trigger on table "public"."notifications" from "service_role";

revoke truncate on table "public"."notifications" from "service_role";

revoke update on table "public"."notifications" from "service_role";

revoke delete on table "public"."organization_access" from "anon";

revoke insert on table "public"."organization_access" from "anon";

revoke references on table "public"."organization_access" from "anon";

revoke select on table "public"."organization_access" from "anon";

revoke trigger on table "public"."organization_access" from "anon";

revoke truncate on table "public"."organization_access" from "anon";

revoke update on table "public"."organization_access" from "anon";

revoke delete on table "public"."organization_access" from "authenticated";

revoke insert on table "public"."organization_access" from "authenticated";

revoke references on table "public"."organization_access" from "authenticated";

revoke select on table "public"."organization_access" from "authenticated";

revoke trigger on table "public"."organization_access" from "authenticated";

revoke truncate on table "public"."organization_access" from "authenticated";

revoke update on table "public"."organization_access" from "authenticated";

revoke delete on table "public"."organization_access" from "service_role";

revoke insert on table "public"."organization_access" from "service_role";

revoke references on table "public"."organization_access" from "service_role";

revoke select on table "public"."organization_access" from "service_role";

revoke trigger on table "public"."organization_access" from "service_role";

revoke truncate on table "public"."organization_access" from "service_role";

revoke update on table "public"."organization_access" from "service_role";

revoke delete on table "public"."organizations" from "anon";

revoke insert on table "public"."organizations" from "anon";

revoke references on table "public"."organizations" from "anon";

revoke select on table "public"."organizations" from "anon";

revoke trigger on table "public"."organizations" from "anon";

revoke truncate on table "public"."organizations" from "anon";

revoke update on table "public"."organizations" from "anon";

revoke delete on table "public"."organizations" from "authenticated";

revoke insert on table "public"."organizations" from "authenticated";

revoke references on table "public"."organizations" from "authenticated";

revoke select on table "public"."organizations" from "authenticated";

revoke trigger on table "public"."organizations" from "authenticated";

revoke truncate on table "public"."organizations" from "authenticated";

revoke update on table "public"."organizations" from "authenticated";

revoke delete on table "public"."organizations" from "service_role";

revoke insert on table "public"."organizations" from "service_role";

revoke references on table "public"."organizations" from "service_role";

revoke select on table "public"."organizations" from "service_role";

revoke trigger on table "public"."organizations" from "service_role";

revoke truncate on table "public"."organizations" from "service_role";

revoke update on table "public"."organizations" from "service_role";

revoke delete on table "public"."package_feature_assignment" from "anon";

revoke insert on table "public"."package_feature_assignment" from "anon";

revoke references on table "public"."package_feature_assignment" from "anon";

revoke select on table "public"."package_feature_assignment" from "anon";

revoke trigger on table "public"."package_feature_assignment" from "anon";

revoke truncate on table "public"."package_feature_assignment" from "anon";

revoke update on table "public"."package_feature_assignment" from "anon";

revoke delete on table "public"."package_feature_assignment" from "authenticated";

revoke insert on table "public"."package_feature_assignment" from "authenticated";

revoke references on table "public"."package_feature_assignment" from "authenticated";

revoke select on table "public"."package_feature_assignment" from "authenticated";

revoke trigger on table "public"."package_feature_assignment" from "authenticated";

revoke truncate on table "public"."package_feature_assignment" from "authenticated";

revoke update on table "public"."package_feature_assignment" from "authenticated";

revoke delete on table "public"."package_feature_assignment" from "service_role";

revoke insert on table "public"."package_feature_assignment" from "service_role";

revoke references on table "public"."package_feature_assignment" from "service_role";

revoke select on table "public"."package_feature_assignment" from "service_role";

revoke trigger on table "public"."package_feature_assignment" from "service_role";

revoke truncate on table "public"."package_feature_assignment" from "service_role";

revoke update on table "public"."package_feature_assignment" from "service_role";

revoke delete on table "public"."package_features" from "anon";

revoke insert on table "public"."package_features" from "anon";

revoke references on table "public"."package_features" from "anon";

revoke select on table "public"."package_features" from "anon";

revoke trigger on table "public"."package_features" from "anon";

revoke truncate on table "public"."package_features" from "anon";

revoke update on table "public"."package_features" from "anon";

revoke delete on table "public"."package_features" from "authenticated";

revoke insert on table "public"."package_features" from "authenticated";

revoke references on table "public"."package_features" from "authenticated";

revoke select on table "public"."package_features" from "authenticated";

revoke trigger on table "public"."package_features" from "authenticated";

revoke truncate on table "public"."package_features" from "authenticated";

revoke update on table "public"."package_features" from "authenticated";

revoke delete on table "public"."package_features" from "service_role";

revoke insert on table "public"."package_features" from "service_role";

revoke references on table "public"."package_features" from "service_role";

revoke select on table "public"."package_features" from "service_role";

revoke trigger on table "public"."package_features" from "service_role";

revoke truncate on table "public"."package_features" from "service_role";

revoke update on table "public"."package_features" from "service_role";

revoke delete on table "public"."poll_options" from "anon";

revoke insert on table "public"."poll_options" from "anon";

revoke references on table "public"."poll_options" from "anon";

revoke select on table "public"."poll_options" from "anon";

revoke trigger on table "public"."poll_options" from "anon";

revoke truncate on table "public"."poll_options" from "anon";

revoke update on table "public"."poll_options" from "anon";

revoke delete on table "public"."poll_options" from "authenticated";

revoke insert on table "public"."poll_options" from "authenticated";

revoke references on table "public"."poll_options" from "authenticated";

revoke select on table "public"."poll_options" from "authenticated";

revoke trigger on table "public"."poll_options" from "authenticated";

revoke truncate on table "public"."poll_options" from "authenticated";

revoke update on table "public"."poll_options" from "authenticated";

revoke delete on table "public"."poll_options" from "service_role";

revoke insert on table "public"."poll_options" from "service_role";

revoke references on table "public"."poll_options" from "service_role";

revoke select on table "public"."poll_options" from "service_role";

revoke trigger on table "public"."poll_options" from "service_role";

revoke truncate on table "public"."poll_options" from "service_role";

revoke update on table "public"."poll_options" from "service_role";

revoke delete on table "public"."poll_votes" from "anon";

revoke insert on table "public"."poll_votes" from "anon";

revoke references on table "public"."poll_votes" from "anon";

revoke select on table "public"."poll_votes" from "anon";

revoke trigger on table "public"."poll_votes" from "anon";

revoke truncate on table "public"."poll_votes" from "anon";

revoke update on table "public"."poll_votes" from "anon";

revoke delete on table "public"."poll_votes" from "authenticated";

revoke insert on table "public"."poll_votes" from "authenticated";

revoke references on table "public"."poll_votes" from "authenticated";

revoke select on table "public"."poll_votes" from "authenticated";

revoke trigger on table "public"."poll_votes" from "authenticated";

revoke truncate on table "public"."poll_votes" from "authenticated";

revoke update on table "public"."poll_votes" from "authenticated";

revoke delete on table "public"."poll_votes" from "service_role";

revoke insert on table "public"."poll_votes" from "service_role";

revoke references on table "public"."poll_votes" from "service_role";

revoke select on table "public"."poll_votes" from "service_role";

revoke trigger on table "public"."poll_votes" from "service_role";

revoke truncate on table "public"."poll_votes" from "service_role";

revoke update on table "public"."poll_votes" from "service_role";

revoke delete on table "public"."polls" from "anon";

revoke insert on table "public"."polls" from "anon";

revoke references on table "public"."polls" from "anon";

revoke select on table "public"."polls" from "anon";

revoke trigger on table "public"."polls" from "anon";

revoke truncate on table "public"."polls" from "anon";

revoke update on table "public"."polls" from "anon";

revoke delete on table "public"."polls" from "authenticated";

revoke insert on table "public"."polls" from "authenticated";

revoke references on table "public"."polls" from "authenticated";

revoke select on table "public"."polls" from "authenticated";

revoke trigger on table "public"."polls" from "authenticated";

revoke truncate on table "public"."polls" from "authenticated";

revoke update on table "public"."polls" from "authenticated";

revoke delete on table "public"."polls" from "service_role";

revoke insert on table "public"."polls" from "service_role";

revoke references on table "public"."polls" from "service_role";

revoke select on table "public"."polls" from "service_role";

revoke trigger on table "public"."polls" from "service_role";

revoke truncate on table "public"."polls" from "service_role";

revoke update on table "public"."polls" from "service_role";

revoke delete on table "public"."profile_billing_assignment" from "anon";

revoke insert on table "public"."profile_billing_assignment" from "anon";

revoke references on table "public"."profile_billing_assignment" from "anon";

revoke select on table "public"."profile_billing_assignment" from "anon";

revoke trigger on table "public"."profile_billing_assignment" from "anon";

revoke truncate on table "public"."profile_billing_assignment" from "anon";

revoke update on table "public"."profile_billing_assignment" from "anon";

revoke delete on table "public"."profile_billing_assignment" from "authenticated";

revoke insert on table "public"."profile_billing_assignment" from "authenticated";

revoke references on table "public"."profile_billing_assignment" from "authenticated";

revoke select on table "public"."profile_billing_assignment" from "authenticated";

revoke trigger on table "public"."profile_billing_assignment" from "authenticated";

revoke truncate on table "public"."profile_billing_assignment" from "authenticated";

revoke update on table "public"."profile_billing_assignment" from "authenticated";

revoke delete on table "public"."profile_billing_assignment" from "service_role";

revoke insert on table "public"."profile_billing_assignment" from "service_role";

revoke references on table "public"."profile_billing_assignment" from "service_role";

revoke select on table "public"."profile_billing_assignment" from "service_role";

revoke trigger on table "public"."profile_billing_assignment" from "service_role";

revoke truncate on table "public"."profile_billing_assignment" from "service_role";

revoke update on table "public"."profile_billing_assignment" from "service_role";

revoke delete on table "public"."profiles" from "anon";

revoke insert on table "public"."profiles" from "anon";

revoke references on table "public"."profiles" from "anon";

revoke select on table "public"."profiles" from "anon";

revoke trigger on table "public"."profiles" from "anon";

revoke truncate on table "public"."profiles" from "anon";

revoke update on table "public"."profiles" from "anon";

revoke delete on table "public"."profiles" from "authenticated";

revoke insert on table "public"."profiles" from "authenticated";

revoke references on table "public"."profiles" from "authenticated";

revoke select on table "public"."profiles" from "authenticated";

revoke trigger on table "public"."profiles" from "authenticated";

revoke truncate on table "public"."profiles" from "authenticated";

revoke update on table "public"."profiles" from "authenticated";

revoke delete on table "public"."profiles" from "service_role";

revoke insert on table "public"."profiles" from "service_role";

revoke references on table "public"."profiles" from "service_role";

revoke select on table "public"."profiles" from "service_role";

revoke trigger on table "public"."profiles" from "service_role";

revoke truncate on table "public"."profiles" from "service_role";

revoke update on table "public"."profiles" from "service_role";

revoke delete on table "public"."user_role_assignment" from "anon";

revoke insert on table "public"."user_role_assignment" from "anon";

revoke references on table "public"."user_role_assignment" from "anon";

revoke select on table "public"."user_role_assignment" from "anon";

revoke trigger on table "public"."user_role_assignment" from "anon";

revoke truncate on table "public"."user_role_assignment" from "anon";

revoke update on table "public"."user_role_assignment" from "anon";

revoke delete on table "public"."user_role_assignment" from "authenticated";

revoke insert on table "public"."user_role_assignment" from "authenticated";

revoke references on table "public"."user_role_assignment" from "authenticated";

revoke select on table "public"."user_role_assignment" from "authenticated";

revoke trigger on table "public"."user_role_assignment" from "authenticated";

revoke truncate on table "public"."user_role_assignment" from "authenticated";

revoke update on table "public"."user_role_assignment" from "authenticated";

revoke delete on table "public"."user_role_assignment" from "service_role";

revoke insert on table "public"."user_role_assignment" from "service_role";

revoke references on table "public"."user_role_assignment" from "service_role";

revoke select on table "public"."user_role_assignment" from "service_role";

revoke trigger on table "public"."user_role_assignment" from "service_role";

revoke truncate on table "public"."user_role_assignment" from "service_role";

revoke update on table "public"."user_role_assignment" from "service_role";

revoke delete on table "public"."user_roles" from "anon";

revoke insert on table "public"."user_roles" from "anon";

revoke references on table "public"."user_roles" from "anon";

revoke select on table "public"."user_roles" from "anon";

revoke trigger on table "public"."user_roles" from "anon";

revoke truncate on table "public"."user_roles" from "anon";

revoke update on table "public"."user_roles" from "anon";

revoke delete on table "public"."user_roles" from "authenticated";

revoke insert on table "public"."user_roles" from "authenticated";

revoke references on table "public"."user_roles" from "authenticated";

revoke select on table "public"."user_roles" from "authenticated";

revoke trigger on table "public"."user_roles" from "authenticated";

revoke truncate on table "public"."user_roles" from "authenticated";

revoke update on table "public"."user_roles" from "authenticated";

revoke delete on table "public"."user_roles" from "service_role";

revoke insert on table "public"."user_roles" from "service_role";

revoke references on table "public"."user_roles" from "service_role";

revoke select on table "public"."user_roles" from "service_role";

revoke trigger on table "public"."user_roles" from "service_role";

revoke truncate on table "public"."user_roles" from "service_role";

revoke update on table "public"."user_roles" from "service_role";

alter table "public"."app_packages" drop constraint "app_packages_package_name_key";

alter table "public"."audit_log" drop constraint "audit_log_changed_by_user_id_fkey";

alter table "public"."billing_entities" drop constraint "billing_entities_ico_key";

alter table "public"."billing_entities" drop constraint "billing_entities_managed_by_profile_id_fkey";

alter table "public"."billing_entities" drop constraint "billing_entities_organization_id_fkey";

alter table "public"."documents" drop constraint "check_entity_link";

alter table "public"."documents" drop constraint "documents_group_id_fkey";

alter table "public"."documents" drop constraint "documents_organization_id_fkey";

alter table "public"."documents" drop constraint "documents_uploaded_by_id_fkey";

alter table "public"."event_participants" drop constraint "event_participants_event_id_fkey";

alter table "public"."event_participants" drop constraint "event_participants_profile_id_fkey";

alter table "public"."events" drop constraint "events_created_by_id_fkey";

alter table "public"."events" drop constraint "events_group_id_fkey";

alter table "public"."group_members" drop constraint "group_members_group_id_fkey";

alter table "public"."group_members" drop constraint "group_members_profile_id_fkey";

alter table "public"."groups" drop constraint "groups_organization_id_fkey";

alter table "public"."groups" drop constraint "groups_owner_profile_id_fkey";

alter table "public"."groups" drop constraint "groups_package_id_fkey";

alter table "public"."groups" drop constraint "groups_theme_id_fkey";

alter table "public"."invoices" drop constraint "invoices_billing_entity_id_fkey";

alter table "public"."invoices" drop constraint "invoices_invoice_number_key";

alter table "public"."invoices" drop constraint "invoices_organization_id_fkey";

alter table "public"."notifications" drop constraint "notifications_recipient_id_fkey";

alter table "public"."organization_access" drop constraint "organization_access_organization_id_fkey";

alter table "public"."organization_access" drop constraint "organization_access_profile_id_fkey";

alter table "public"."organizations" drop constraint "organizations_owner_profile_id_fkey";

alter table "public"."organizations" drop constraint "organizations_package_id_fkey";

alter table "public"."organizations" drop constraint "organizations_theme_id_fkey";

alter table "public"."package_feature_assignment" drop constraint "package_feature_assignment_feature_id_fkey";

alter table "public"."package_feature_assignment" drop constraint "package_feature_assignment_package_id_fkey";

alter table "public"."package_features" drop constraint "package_features_feature_key_key";

alter table "public"."poll_options" drop constraint "poll_options_poll_id_fkey";

alter table "public"."poll_votes" drop constraint "poll_votes_option_id_fkey";

alter table "public"."poll_votes" drop constraint "poll_votes_poll_id_fkey";

alter table "public"."poll_votes" drop constraint "poll_votes_voter_id_fkey";

alter table "public"."polls" drop constraint "polls_created_by_id_fkey";

alter table "public"."polls" drop constraint "polls_group_id_fkey";

alter table "public"."profile_billing_assignment" drop constraint "profile_billing_assignment_billing_entity_id_fkey";

alter table "public"."profile_billing_assignment" drop constraint "profile_billing_assignment_profile_id_fkey";

alter table "public"."profiles" drop constraint "profiles_email_key";

alter table "public"."user_role_assignment" drop constraint "user_role_assignment_profile_id_fkey";

alter table "public"."user_role_assignment" drop constraint "user_role_assignment_role_id_fkey";

alter table "public"."user_roles" drop constraint "user_roles_role_name_key";

drop function if exists "public"."audit_trigger_func"();

drop function if exists "public"."handle_new_user"();

drop function if exists "public"."is_superadmin"();

alter table "public"."app_packages" drop constraint "app_packages_pkey";

alter table "public"."audit_log" drop constraint "audit_log_pkey";

alter table "public"."billing_entities" drop constraint "billing_entities_pkey";

alter table "public"."color_themes" drop constraint "color_themes_pkey";

alter table "public"."documents" drop constraint "documents_pkey";

alter table "public"."event_participants" drop constraint "event_participants_pkey";

alter table "public"."events" drop constraint "events_pkey";

alter table "public"."group_members" drop constraint "group_members_pkey";

alter table "public"."groups" drop constraint "groups_pkey";

alter table "public"."invoices" drop constraint "invoices_pkey";

alter table "public"."notifications" drop constraint "notifications_pkey";

alter table "public"."organization_access" drop constraint "organization_access_pkey";

alter table "public"."organizations" drop constraint "organizations_pkey";

alter table "public"."package_feature_assignment" drop constraint "package_feature_assignment_pkey";

alter table "public"."package_features" drop constraint "package_features_pkey";

alter table "public"."poll_options" drop constraint "poll_options_pkey";

alter table "public"."poll_votes" drop constraint "poll_votes_pkey";

alter table "public"."polls" drop constraint "polls_pkey";

alter table "public"."profile_billing_assignment" drop constraint "profile_billing_assignment_pkey";

alter table "public"."profiles" drop constraint "profiles_pkey";

alter table "public"."user_role_assignment" drop constraint "user_role_assignment_pkey";

alter table "public"."user_roles" drop constraint "user_roles_pkey";

drop index if exists "public"."app_packages_package_name_key";

drop index if exists "public"."app_packages_pkey";

drop index if exists "public"."audit_log_pkey";

drop index if exists "public"."billing_entities_ico_key";

drop index if exists "public"."billing_entities_pkey";

drop index if exists "public"."color_themes_pkey";

drop index if exists "public"."documents_pkey";

drop index if exists "public"."event_participants_pkey";

drop index if exists "public"."events_pkey";

drop index if exists "public"."group_members_pkey";

drop index if exists "public"."groups_pkey";

drop index if exists "public"."invoices_invoice_number_key";

drop index if exists "public"."invoices_pkey";

drop index if exists "public"."notifications_pkey";

drop index if exists "public"."organization_access_pkey";

drop index if exists "public"."organizations_pkey";

drop index if exists "public"."package_feature_assignment_pkey";

drop index if exists "public"."package_features_feature_key_key";

drop index if exists "public"."package_features_pkey";

drop index if exists "public"."poll_options_pkey";

drop index if exists "public"."poll_votes_pkey";

drop index if exists "public"."polls_pkey";

drop index if exists "public"."profile_billing_assignment_pkey";

drop index if exists "public"."profiles_email_key";

drop index if exists "public"."profiles_pkey";

drop index if exists "public"."user_role_assignment_pkey";

drop index if exists "public"."user_roles_pkey";

drop index if exists "public"."user_roles_role_name_key";

drop table "public"."app_packages";

drop table "public"."audit_log";

drop table "public"."billing_entities";

drop table "public"."color_themes";

drop table "public"."documents";

drop table "public"."event_participants";

drop table "public"."events";

drop table "public"."group_members";

drop table "public"."groups";

drop table "public"."invoices";

drop table "public"."notifications";

drop table "public"."organization_access";

drop table "public"."organizations";

drop table "public"."package_feature_assignment";

drop table "public"."package_features";

drop table "public"."poll_options";

drop table "public"."poll_votes";

drop table "public"."polls";

drop table "public"."profile_billing_assignment";

drop table "public"."profiles";

drop table "public"."user_role_assignment";

drop table "public"."user_roles";

drop type "public"."attendance_status";

drop type "public"."entity_role";

drop type "public"."invoice_status";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.handle_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$function$
;


drop trigger if exists "on_auth_user_created" on "auth"."users";

CREATE TRIGGER enforce_bucket_name_length_trigger BEFORE INSERT OR UPDATE OF name ON storage.buckets FOR EACH ROW EXECUTE FUNCTION storage.enforce_bucket_name_length();

CREATE TRIGGER objects_delete_delete_prefix AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();

CREATE TRIGGER objects_insert_create_prefix BEFORE INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.objects_insert_prefix_trigger();

CREATE TRIGGER objects_update_create_prefix BEFORE UPDATE ON storage.objects FOR EACH ROW WHEN (((new.name <> old.name) OR (new.bucket_id <> old.bucket_id))) EXECUTE FUNCTION storage.objects_update_prefix_trigger();

CREATE TRIGGER prefixes_create_hierarchy BEFORE INSERT ON storage.prefixes FOR EACH ROW WHEN ((pg_trigger_depth() < 1)) EXECUTE FUNCTION storage.prefixes_insert_trigger();

CREATE TRIGGER prefixes_delete_hierarchy AFTER DELETE ON storage.prefixes FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();



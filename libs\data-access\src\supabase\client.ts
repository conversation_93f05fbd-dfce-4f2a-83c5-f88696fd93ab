import {
  createClient,
  SupabaseClient,
  processLock,
} from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Rozlíšime podľa prostredia
const isExpo =
  typeof navigator !== 'undefined' && navigator.product === 'ReactNative';

const supabaseUrl = isExpo
  ? process.env.EXPO_PUBLIC_SUPABASE_URL
  : process.env.NEXT_PUBLIC_SUPABASE_URL;

const supabaseKey = isExpo
  ? process.env.EXPO_PUBLIC_SUPABASE_KEY
  : process.env.NEXT_PUBLIC_SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error(
    `Missing Supabase environment variables. URL: ${
      supabaseUrl ? 'SET' : 'MISSING'
    }, Key: ${supabaseKey ? 'SET' : 'MISSING'}`
  );
}

export const getClient = (): SupabaseClient => {
  return createClient(supabaseUrl, supabaseKey, {
    auth: {
      storage: isExpo ? AsyncStorage : undefined,
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: !isExpo, // Expo nepotrebuje URL callback
      flowType: 'pkce',
      storageKey: 'supabase.auth.token',
      lock: isExpo ? processLock : undefined,
    },
  });
};

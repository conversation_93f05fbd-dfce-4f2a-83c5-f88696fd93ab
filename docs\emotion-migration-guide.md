# Emotion Migration Guide

Projekt bol úspešne migrovaný z styled-components na @emotion/styled.

## Čo sa zmenilo

### Dependencies
- **Odstránené**: `styled-components`, `@types/styled-components`, `babel-plugin-styled-components`, `@swc/plugin-styled-components`
- **Pridané**: `@emotion/styled`, `@emotion/react`, `@emotion/cache`, `@emotion/native`, `@emotion/babel-plugin`

### Konfigurácia

#### Next.js (dohoder-admin)
- `next.config.js`: Z<PERSON><PERSON><PERSON> z `styledComponents: true` na `emotion: true`
- `app/registry.tsx`: Nahradený StyledComponentsRegistry s EmotionRegistry
- `app/layout.tsx`: Používa EmotionRegistry namiesto StyledComponentsRegistry
- `tsconfig.json`: Pridané `jsxImportSource: "@emotion/react"`

#### React Native (dohoder-mobile)
- `babel.config.js`: Pridaný @emotion/babel-plugin
- `tsconfig.app.json`: Pridané `jsxImportSource: "@emotion/react"`

#### Nx konfigurácia
- `nx.json`: Zmenené z `"style": "styled-components"` na `"style": "@emotion/styled"`

## Ako používať Emotion

### 1. Styled Components (rovnaká syntax ako styled-components)
```tsx
import styled from '@emotion/styled';

const Button = styled.button`
  background: blue;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  
  &:hover {
    background: darkblue;
  }
`;
```

### 2. CSS Prop (nová funkcia)
```tsx
/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';

const MyComponent = () => (
  <div
    css={css`
      color: red;
      background: blue;
    `}
  >
    Content
  </div>
);
```

### 3. React Native (s @emotion/native)
```tsx
import styled from '@emotion/native';

const StyledView = styled.View`
  background-color: blue;
  padding: 20px;
`;

const StyledText = styled.Text`
  color: white;
  font-size: 16px;
`;
```

## Výhody Emotion oproti styled-components

1. **Lepšia performance** - menší bundle size
2. **CSS prop** - možnosť písať CSS priamo v JSX
3. **Lepšia TypeScript podpora**
4. **Aktívnejší vývoj** a komunita
5. **Kompatibilita** - rovnaká syntax pre styled komponenty

## Testovanie

Migrácia bola otestovaná:
- ✅ TypeScript kompilácia bez chýb
- ✅ Admin aplikácia testy (jediná chyba súvisí s QueryClient, nie s Emotion)
- ✅ Mobile aplikácia testy (jediná chyba súvisí s AsyncStorage, nie s Emotion)
- ✅ Emotion syntax funguje správne

## Ďalšie kroky

Teraz môžete:
1. Začať používať CSS prop pre jednoduchšie styling
2. Migrovať existujúce styled komponenty (ak nejaké máte)
3. Využiť lepšie performance a menší bundle size

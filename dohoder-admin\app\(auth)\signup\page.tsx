'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// PREDPOKLAD: V<PERSON><PERSON> custom hook na získanie Supabase klienta
import { getClient } from '@/data-access/supabase/client';

export default function SignUpPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const supabase = getClient();
  const router = useRouter();

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    // 1. Zavolanie Supabase signUp
    // V lokálnom prostredí je e-mailová verifikácia automaticky preskočená
    const {
      data: { user },
      error: signUpError,
    } = await supabase.auth.signUp({
      email,
      password,
    });

    if (signUpError) {
      // 2. Spracovanie chyby
      setError(signUpError.message);
      console.error('Registration failed:', signUpError.message);
      setIsLoading(false);
      return;
    }

    // 3. Úspešná registrácia

    // Keďže Supabase pri registrácii automaticky prihlási,
    // automaticky Vám v auth.users vytvorilo NOVÉ UUID.

    // Teraz by Váš trigger (ktorý zatiaľ nemáme) mal automaticky
    // vytvoriť aj riadok v tabuľke 'profiles' s týmto NOVÝM UUID.

    // Zatiaľ to riešime len presmerovaním.
    if (user) {
      // Presmerujeme na dashboard, AuthGuard (ak existuje) prevezme riadenie
      router.push('/dashboard');
    } else {
      // Malo by byť len pri zlyhaní, kedy je signUpError
      setError('Chyba registrácie. Skúste sa prihlásiť.');
    }

    setIsLoading(false);
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded shadow-md w-full max-w-sm">
        <h1 className="text-2xl font-bold mb-6 text-center">Registrácia</h1>

        <form onSubmit={handleSignUp}>
          <div className="mb-4">
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700"
            >
              E-mail
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            />
          </div>

          <div className="mb-6">
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700"
            >
              Heslo
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              minLength={6}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm"
            />
          </div>

          {error && (
            <p className="text-red-500 text-sm mb-4 border border-red-200 p-2 rounded bg-red-50">
              {error}
            </p>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className={`w-full py-2 px-4 rounded font-semibold text-white ${
              isLoading
                ? 'bg-indigo-400 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700'
            }`}
          >
            {isLoading ? 'Registrujem...' : 'Registrovať sa'}
          </button>
        </form>

        <p className="mt-4 text-center text-sm text-gray-600">
          Už máte účet?{' '}
          <Link
            href="/login"
            className="text-indigo-600 hover:text-indigo-800 font-medium"
          >
            Prihlásiť sa
          </Link>
        </p>
      </div>
    </div>
  );
}

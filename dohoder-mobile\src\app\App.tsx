import "@/i18m"
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryProvider } from '../providers/QueryProvider';
import { TodoScreen } from '../screens/TodoScreen';

export const App = () => {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />
      <QueryProvider>
        <TodoScreen />
      </QueryProvider>
    </SafeAreaProvider>
  );
};

export default App;

export interface Category {
    id: string;
    name: string;
    color: string;
    icon: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface CreateCategoryRequest {
    name: string;
    color?: string;
    icon?: string;
    userId: string;
}
export interface UpdateCategoryRequest {
    name?: string;
    color?: string;
    icon?: string;
}
//# sourceMappingURL=category.d.ts.map